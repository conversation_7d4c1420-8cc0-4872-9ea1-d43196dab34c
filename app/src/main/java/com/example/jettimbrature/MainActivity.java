package com.example.jettimbrature;

import android.app.PendingIntent;
import android.content.Intent;
import android.content.IntentFilter;
import android.nfc.NdefMessage;
import android.nfc.NdefRecord;
import android.nfc.NfcAdapter;
import android.nfc.Tag;
import android.nfc.tech.Ndef;
import android.nfc.tech.NdefFormatable;
import android.os.Bundle;
import android.os.Parcelable;
import android.view.View;
import android.widget.Button;
import android.widget.TextView;
import android.widget.Toast;

import androidx.activity.EdgeToEdge;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.graphics.Insets;
import androidx.core.view.ViewCompat;
import androidx.core.view.WindowInsetsCompat;

import java.io.UnsupportedEncodingException;
import java.util.Arrays;

public class MainActivity extends AppCompatActivity {

    private NfcAdapter nfcAdapter;
    private PendingIntent pendingIntent;
    private IntentFilter[] intentFiltersArray;
    private String[][] techListsArray;

    private TextView statusTextView;
    private TextView dataTextView;
    private Button clearButton;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        EdgeToEdge.enable(this);
        setContentView(R.layout.activity_main);
        ViewCompat.setOnApplyWindowInsetsListener(findViewById(R.id.main), (v, insets) -> {
            Insets systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars());
            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom);
            return insets;
        });

        // Inizializza le view
        statusTextView = findViewById(R.id.statusTextView);
        dataTextView = findViewById(R.id.dataTextView);
        clearButton = findViewById(R.id.clearButton);

        // Configura il pulsante per cancellare i dati
        clearButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                clearData();
            }
        });

        // Inizializza NFC
        initializeNFC();

        // Gestisce l'intent se l'app è stata aperta tramite NFC
        handleIntent(getIntent());
    }

    private void initializeNFC() {
        nfcAdapter = NfcAdapter.getDefaultAdapter(this);

        if (nfcAdapter == null) {
            statusTextView.setText("NFC non supportato su questo dispositivo");
            Toast.makeText(this, "NFC non supportato", Toast.LENGTH_LONG).show();
            return;
        }

        if (!nfcAdapter.isEnabled()) {
            statusTextView.setText("NFC disabilitato. Abilitalo nelle impostazioni.");
            Toast.makeText(this, "Abilita NFC nelle impostazioni", Toast.LENGTH_LONG).show();
            return;
        }

        // Configura il PendingIntent per gestire i tag NFC
        Intent intent = new Intent(this, getClass());
        intent.addFlags(Intent.FLAG_ACTIVITY_SINGLE_TOP | Intent.FLAG_ACTIVITY_CLEAR_TOP);

        pendingIntent = PendingIntent.getActivity(this, 0, intent, PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_MUTABLE);

        // Configura i filtri per i diversi tipi di tag NFC
        IntentFilter ndef = new IntentFilter(NfcAdapter.ACTION_NDEF_DISCOVERED);
        IntentFilter tagDiscovered = new IntentFilter(NfcAdapter.ACTION_TAG_DISCOVERED);
        IntentFilter techDiscovered = new IntentFilter(NfcAdapter.ACTION_TECH_DISCOVERED);

        try {
            ndef.addDataType("*/*");
        } catch (IntentFilter.MalformedMimeTypeException e) {
            throw new RuntimeException("fail", e);
        }

        intentFiltersArray = new IntentFilter[] {ndef, tagDiscovered, techDiscovered};
        techListsArray = new String[][] {
            new String[] { Ndef.class.getName() },
            new String[] { NdefFormatable.class.getName() }
        };

        statusTextView.setText("NFC pronto. Avvicina un badge...");
    }

    @Override
    protected void onResume() {
        super.onResume();
        if (nfcAdapter != null && nfcAdapter.isEnabled()) {
            // Disabilita prima per sicurezza
            nfcAdapter.disableForegroundDispatch(this);
            // Poi riabilita
            nfcAdapter.enableForegroundDispatch(this, pendingIntent, intentFiltersArray, techListsArray);
            statusTextView.setText("NFC pronto. Avvicina un badge...");
        }
    }

    @Override
    protected void onPause() {
        super.onPause();
        if (nfcAdapter != null) {
            nfcAdapter.disableForegroundDispatch(this);
        }
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        // Debug: conferma che onNewIntent viene chiamato
        Toast.makeText(this, "onNewIntent chiamato!", Toast.LENGTH_SHORT).show();
        // Imposta il nuovo intent come intent corrente
        setIntent(intent);
        handleIntent(intent);
    }

    private void handleIntent(Intent intent) {
        String action = intent.getAction();
        if (NfcAdapter.ACTION_NDEF_DISCOVERED.equals(action) ||
            NfcAdapter.ACTION_TAG_DISCOVERED.equals(action) ||
            NfcAdapter.ACTION_TECH_DISCOVERED.equals(action)) {

            // Debug: mostra che il tag è stato rilevato
            Toast.makeText(this, "Tag NFC rilevato!", Toast.LENGTH_SHORT).show();

            Tag tag = intent.getParcelableExtra(NfcAdapter.EXTRA_TAG);
            if (tag != null) {
                readFromTag(tag);
            }
        }
    }

    private void readFromTag(Tag tag) {
        statusTextView.setText("Badge NFC rilevato! Lettura in corso...");

        StringBuilder data = new StringBuilder();
        data.append("=== INFORMAZIONI TAG NFC ===\n\n");

        // Informazioni di base del tag
        data.append("ID Tag: ").append(bytesToHex(tag.getId())).append("\n");
        data.append("Tecnologie supportate: ").append(Arrays.toString(tag.getTechList())).append("\n\n");

        // Prova a leggere i dati NDEF
        Ndef ndef = Ndef.get(tag);
        if (ndef != null) {
            try {
                ndef.connect();
                NdefMessage ndefMessage = ndef.getNdefMessage();
                if (ndefMessage != null) {
                    data.append("=== DATI NDEF ===\n");
                    NdefRecord[] records = ndefMessage.getRecords();
                    for (int i = 0; i < records.length; i++) {
                        data.append("Record ").append(i + 1).append(":\n");
                        data.append("  Tipo: ").append(new String(records[i].getType())).append("\n");
                        data.append("  Payload: ").append(getTextFromNdefRecord(records[i])).append("\n");
                        data.append("  Payload (hex): ").append(bytesToHex(records[i].getPayload())).append("\n\n");
                    }
                } else {
                    data.append("Nessun messaggio NDEF trovato\n\n");
                }
                ndef.close();
            } catch (Exception e) {
                data.append("Errore nella lettura NDEF: ").append(e.getMessage()).append("\n\n");
            }
        } else {
            data.append("Tag non supporta NDEF\n\n");
        }

        // Informazioni aggiuntive
        data.append("=== INFORMAZIONI TECNICHE ===\n");
        if (ndef != null) {
            data.append("Dimensione massima: ").append(ndef.getMaxSize()).append(" bytes\n");
            data.append("Tipo NDEF: ").append(ndef.getType()).append("\n");
            data.append("Scrivibile: ").append(ndef.isWritable() ? "Sì" : "No").append("\n");
        }

        dataTextView.setText(data.toString());
        statusTextView.setText("Badge letto con successo!");
    }

    private void clearData() {
        dataTextView.setText("");
        statusTextView.setText("Dati cancellati. Pronto per un nuovo badge...");
    }

    private String bytesToHex(byte[] bytes) {
        StringBuilder result = new StringBuilder();
        for (byte b : bytes) {
            result.append(String.format("%02X ", b));
        }
        return result.toString().trim();
    }

    private String getTextFromNdefRecord(NdefRecord record) {
        try {
            byte[] payload = record.getPayload();

            // Controlla se è un record di testo
            if (record.getTnf() == NdefRecord.TNF_WELL_KNOWN &&
                Arrays.equals(record.getType(), NdefRecord.RTD_TEXT)) {

                // Il primo byte contiene informazioni sulla codifica e lunghezza del codice lingua
                String textEncoding = ((payload[0] & 128) == 0) ? "UTF-8" : "UTF-16";
                int languageCodeLength = payload[0] & 0063;

                // Estrae il testo saltando il codice lingua
                return new String(payload, languageCodeLength + 1,
                                payload.length - languageCodeLength - 1, textEncoding);
            } else {
                // Per altri tipi di record, prova a interpretare come testo UTF-8
                return new String(payload, "UTF-8");
            }
        } catch (UnsupportedEncodingException e) {
            return "Errore nella decodifica: " + e.getMessage();
        }
    }
}