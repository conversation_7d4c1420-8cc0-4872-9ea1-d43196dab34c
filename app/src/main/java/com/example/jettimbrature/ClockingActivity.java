package com.example.jettimbrature;

import android.app.PendingIntent;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.SharedPreferences;
import android.net.Uri;
import android.nfc.NdefMessage;
import android.nfc.NdefRecord;
import android.nfc.NfcAdapter;
import android.nfc.Tag;
import android.nfc.tech.Ndef;
import android.nfc.tech.NdefFormatable;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.os.Parcelable;
import android.util.Log;
import android.view.View;
import android.widget.Button;
import android.widget.TextView;
import android.widget.Toast;

import androidx.activity.EdgeToEdge;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.graphics.Insets;
import androidx.core.view.ViewCompat;
import androidx.core.view.WindowInsetsCompat;

import java.io.UnsupportedEncodingException;
import java.util.Arrays;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

public class ClockingActivity extends AppCompatActivity {

    private static final String TAG = "JetTimbrature";

    // SharedPreferences per leggere il token
    private static final String PREFS_NAME = "JetTimbraturePrefs";
    private static final String TOKEN_KEY = "auth_token";

    private NfcAdapter nfcAdapter;
    private PendingIntent pendingIntent;
    private IntentFilter[] intentFiltersArray;
    private String[][] techListsArray;

    private TextView statusTextView;
    private TextView dataTextView;
    private TextView tokenTextView;

    // Timer per cancellazione automatica
    private Handler clearDataHandler;
    private Runnable clearDataRunnable;
    private static final int CLEAR_DELAY_MS = 5000; // 5 secondi

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        EdgeToEdge.enable(this);
        setContentView(R.layout.activity_clocking);
        ViewCompat.setOnApplyWindowInsetsListener(findViewById(R.id.main), (v, insets) -> {
            Insets systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars());
            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom);
            return insets;
        });

        // Inizializza le view
        statusTextView = findViewById(R.id.statusTextView);
        dataTextView = findViewById(R.id.dataTextView);
        tokenTextView = findViewById(R.id.tokenTextView);

        // Inizializza il timer per la cancellazione automatica
        clearDataHandler = new Handler(Looper.getMainLooper());
        clearDataRunnable = new Runnable() {
            @Override
            public void run() {
                clearData();
            }
        };

        // Carica e mostra il token salvato
        loadAndShowSavedToken();

        // Gestisce l'URL se l'app è stata aperta tramite deep link
        handleUrlIntent(getIntent());

        // Inizializza NFC
        initializeNFC();

        // Gestisce l'intent se l'app è stata aperta tramite NFC
        handleIntent(getIntent());
    }

    private void initializeNFC() {
        nfcAdapter = NfcAdapter.getDefaultAdapter(this);

        if (nfcAdapter == null) {
            statusTextView.setText("NFC non supportato su questo dispositivo");
            Toast.makeText(this, "NFC non supportato", Toast.LENGTH_LONG).show();
            return;
        }

        if (!nfcAdapter.isEnabled()) {
            statusTextView.setText("NFC disabilitato. Abilitalo nelle impostazioni.");
            Toast.makeText(this, "Abilita NFC nelle impostazioni", Toast.LENGTH_LONG).show();
            return;
        }

        // Configura il PendingIntent per gestire i tag NFC
        Intent intent = new Intent(this, getClass());
        intent.addFlags(Intent.FLAG_ACTIVITY_SINGLE_TOP | Intent.FLAG_ACTIVITY_CLEAR_TOP);

        pendingIntent = PendingIntent.getActivity(this, 0, intent, PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_MUTABLE);

        // Configura i filtri per i diversi tipi di tag NFC
        IntentFilter ndef = new IntentFilter(NfcAdapter.ACTION_NDEF_DISCOVERED);
        IntentFilter tagDiscovered = new IntentFilter(NfcAdapter.ACTION_TAG_DISCOVERED);
        IntentFilter techDiscovered = new IntentFilter(NfcAdapter.ACTION_TECH_DISCOVERED);

        try {
            ndef.addDataType("*/*");
        } catch (IntentFilter.MalformedMimeTypeException e) {
            throw new RuntimeException("fail", e);
        }

        intentFiltersArray = new IntentFilter[] {ndef, tagDiscovered, techDiscovered};
        techListsArray = new String[][] {
            new String[] { Ndef.class.getName() },
            new String[] { NdefFormatable.class.getName() }
        };

        statusTextView.setText("NFC pronto. Avvicina un badge...");
    }

    @Override
    protected void onResume() {
        super.onResume();
        if (nfcAdapter != null && nfcAdapter.isEnabled()) {
            // Disabilita prima per sicurezza
            nfcAdapter.disableForegroundDispatch(this);
            // Poi riabilita
            nfcAdapter.enableForegroundDispatch(this, pendingIntent, intentFiltersArray, techListsArray);
            statusTextView.setText("NFC pronto. Avvicina un badge...");
        }
    }

    @Override
    protected void onPause() {
        super.onPause();
        if (nfcAdapter != null) {
            nfcAdapter.disableForegroundDispatch(this);
        }
        // Ferma il timer quando l'app va in background
        stopAutoClearTimer();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        // Pulisce il timer quando l'Activity viene distrutta
        stopAutoClearTimer();
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        // Debug: conferma che onNewIntent viene chiamato
        Toast.makeText(this, "onNewIntent chiamato!", Toast.LENGTH_SHORT).show();
        // Imposta il nuovo intent come intent corrente
        setIntent(intent);

        // Gestisce prima l'URL se presente
        handleUrlIntent(intent);

        // Poi gestisce l'intent NFC
        handleIntent(intent);
    }

    private void handleUrlIntent(Intent intent) {
        String action = intent.getAction();
        Uri data = intent.getData();

        // Log delle informazioni di action e data
        Log.d(TAG, "=== handleUrlIntent ===");
        Log.d(TAG, "Action: " + action);
        Log.d(TAG, "Data: " + data);

        if (data != null) {
            Log.d(TAG, "Data details:");
            Log.d(TAG, "  Scheme: " + data.getScheme());
            Log.d(TAG, "  Host: " + data.getHost());
            Log.d(TAG, "  Path: " + data.getPath());
            Log.d(TAG, "  Query: " + data.getQuery());
            Log.d(TAG, "  Fragment: " + data.getFragment());
            Log.d(TAG, "  Full URI: " + data.toString());
        } else {
            Log.d(TAG, "Data is null");
        }

        if (Intent.ACTION_VIEW.equals(action) && data != null) {
            String token = null;

            // Verifica schema HTTPS
            if ("https".equals(data.getScheme()) &&
                "timbrature.jethr.com".equals(data.getHost()) &&
                data.getPath() != null && data.getPath().startsWith("/attiva/")) {

                Log.d(TAG, "HTTPS URL format is correct, extracting token...");
                String path = data.getPath();
                token = path.substring("/attiva/".length());

            }
            // Verifica schema personalizzato per test
            else if ("jettimbrature".equals(data.getScheme()) && "attiva".equals(data.getHost())) {
                Log.d(TAG, "Custom scheme detected, extracting token...");
                // Per schema personalizzato: jettimbrature://attiva/ABC123
                String path = data.getPath();
                if (path != null && path.startsWith("/")) {
                    token = path.substring(1); // Rimuove il "/" iniziale
                }
            } else {
                Log.w(TAG, "URL format does not match any expected pattern");
                Log.d(TAG, "  Scheme: " + data.getScheme());
                Log.d(TAG, "  Host: " + data.getHost());
                Log.d(TAG, "  Path: " + data.getPath());
            }

            // Processa il token se trovato
            if (token != null) {
                Log.d(TAG, "Extracted token: '" + token + "'");
                Log.d(TAG, "Token length: " + token.length());
                Log.d(TAG, "Token is valid: " + isValidToken(token));

                if (!token.trim().isEmpty() && isValidToken(token)) {
                    // Token valido - mostra il token
                    Log.d(TAG, "Token validation successful");
                    showToken(token);
                    Toast.makeText(this, "Autenticazione riuscita", Toast.LENGTH_LONG).show();
                } else {
                    // Token non valido o mancante
                    Log.w(TAG, "Token validation failed");
                    showAuthenticationError();
                }
            }
        }
    }

    private boolean isValidToken(String token) {
        // Verifica che il token sia alfanumerico e non vuoto
        return token != null && token.matches("^[a-zA-Z0-9]+$") && token.length() > 0;
    }

    private void showToken(String token) {
        tokenTextView.setText("Token: " + token);
        tokenTextView.setVisibility(View.VISIBLE);
    }

    private void showAuthenticationError() {
        tokenTextView.setText("Autenticazione fallita");
        tokenTextView.setTextColor(getResources().getColor(android.R.color.holo_red_dark));
        tokenTextView.setBackgroundColor(getResources().getColor(android.R.color.holo_red_light));
        tokenTextView.setVisibility(View.VISIBLE);

        // Mostra anche un toast per maggiore visibilità
        Toast.makeText(this, "Autenticazione fallita", Toast.LENGTH_LONG).show();

        // Disabilita la funzionalità NFC in caso di errore di autenticazione
        statusTextView.setText("Autenticazione fallita. Impossibile utilizzare l'app.");
    }

    private void handleIntent(Intent intent) {
        String action = intent.getAction();
        if (NfcAdapter.ACTION_NDEF_DISCOVERED.equals(action) ||
            NfcAdapter.ACTION_TAG_DISCOVERED.equals(action) ||
            NfcAdapter.ACTION_TECH_DISCOVERED.equals(action)) {

            // Verifica se l'autenticazione è fallita
            if (isAuthenticationFailed()) {
                Toast.makeText(this, "Autenticazione fallita. Impossibile leggere tag NFC.", Toast.LENGTH_LONG).show();
                return;
            }

            // Debug: mostra che il tag è stato rilevato
            Toast.makeText(this, "Tag NFC rilevato!", Toast.LENGTH_SHORT).show();

            Tag tag = intent.getParcelableExtra(NfcAdapter.EXTRA_TAG);
            if (tag != null) {
                readFromTag(tag);
            }
        }
    }

    private boolean isAuthenticationFailed() {
        // Verifica se il token TextView mostra un errore di autenticazione
        return tokenTextView.getVisibility() == View.VISIBLE &&
               "Autenticazione fallita".equals(tokenTextView.getText().toString());
    }

    private void readFromTag(Tag tag) {
        statusTextView.setText("Badge NFC rilevato! Lettura in corso...");

        try {
            JSONObject nfcData = new JSONObject();

            // Timestamp della lettura
            nfcData.put("timestamp", System.currentTimeMillis());
            nfcData.put("timestamp_readable", new java.util.Date().toString());

            // Informazioni di base del tag
            JSONObject tagInfo = new JSONObject();
            tagInfo.put("id", bytesToHex(tag.getId()));
            tagInfo.put("id_bytes", bytesToHexArray(tag.getId()));

            // Tecnologie supportate
            JSONArray techArray = new JSONArray();
            for (String tech : tag.getTechList()) {
                techArray.put(tech);
            }
            tagInfo.put("supported_technologies", techArray);

            nfcData.put("tag_info", tagInfo);

            // Prova a leggere i dati NDEF
            Ndef ndef = Ndef.get(tag);
            JSONObject ndefInfo = new JSONObject();

            if (ndef != null) {
                try {
                    ndef.connect();

                    // Informazioni tecniche NDEF
                    ndefInfo.put("max_size", ndef.getMaxSize());
                    ndefInfo.put("type", ndef.getType());
                    ndefInfo.put("writable", ndef.isWritable());
                    ndefInfo.put("connected", ndef.isConnected());

                    NdefMessage ndefMessage = ndef.getNdefMessage();
                    if (ndefMessage != null) {
                        JSONArray recordsArray = new JSONArray();
                        NdefRecord[] records = ndefMessage.getRecords();

                        for (int i = 0; i < records.length; i++) {
                            JSONObject recordObj = new JSONObject();
                            NdefRecord record = records[i];

                            recordObj.put("record_number", i + 1);
                            recordObj.put("tnf", record.getTnf());
                            recordObj.put("tnf_description", getTnfDescription(record.getTnf()));
                            recordObj.put("type", new String(record.getType()));
                            recordObj.put("type_hex", bytesToHex(record.getType()));
                            recordObj.put("id", record.getId() != null ? new String(record.getId()) : null);
                            recordObj.put("payload_text", getTextFromNdefRecord(record));
                            recordObj.put("payload_hex", bytesToHex(record.getPayload()));
                            recordObj.put("payload_bytes", bytesToHexArray(record.getPayload()));
                            recordObj.put("payload_length", record.getPayload().length);

                            recordsArray.put(recordObj);
                        }

                        ndefInfo.put("records", recordsArray);
                        ndefInfo.put("records_count", records.length);
                        ndefInfo.put("message_length", ndefMessage.getByteArrayLength());
                    } else {
                        ndefInfo.put("message", "No NDEF message found");
                        ndefInfo.put("records", new JSONArray());
                        ndefInfo.put("records_count", 0);
                    }

                    ndef.close();
                } catch (Exception e) {
                    ndefInfo.put("error", "Error reading NDEF: " + e.getMessage());
                    ndefInfo.put("error_type", e.getClass().getSimpleName());
                }
            } else {
                ndefInfo.put("supported", false);
                ndefInfo.put("message", "Tag does not support NDEF");
            }

            nfcData.put("ndef_info", ndefInfo);

            // Mostra il JSON formattato
            String jsonString = nfcData.toString(2); // Indentazione di 2 spazi
            dataTextView.setText(jsonString);
            statusTextView.setText("Badge letto con successo! JSON generato. (Cancellazione automatica in 5s)");

            // Avvia il timer per la cancellazione automatica
            startAutoClearTimer();

        } catch (JSONException e) {
            dataTextView.setText("Errore nella creazione del JSON: " + e.getMessage());
            statusTextView.setText("Errore nella lettura del badge");

            // Avvia il timer anche in caso di errore
            startAutoClearTimer();
        }
    }

    private void startAutoClearTimer() {
        // Cancella il timer precedente se esiste
        stopAutoClearTimer();

        // Avvia un nuovo timer
        clearDataHandler.postDelayed(clearDataRunnable, CLEAR_DELAY_MS);
    }

    private void stopAutoClearTimer() {
        // Rimuove il callback pendente se esiste
        clearDataHandler.removeCallbacks(clearDataRunnable);
    }

    private void clearData() {
        dataTextView.setText("");
        statusTextView.setText("Dati cancellati automaticamente. Pronto per un nuovo badge...");

        // Ferma il timer quando i dati vengono cancellati
        stopAutoClearTimer();
    }

    private String bytesToHex(byte[] bytes) {
        StringBuilder result = new StringBuilder();
        for (byte b : bytes) {
            result.append(String.format("%02X ", b));
        }
        return result.toString().trim();
    }

    private JSONArray bytesToHexArray(byte[] bytes) {
        JSONArray array = new JSONArray();
        for (byte b : bytes) {
            array.put(String.format("%02X", b));
        }
        return array;
    }

    private String getTnfDescription(short tnf) {
        switch (tnf) {
            case NdefRecord.TNF_EMPTY:
                return "Empty";
            case NdefRecord.TNF_WELL_KNOWN:
                return "Well Known";
            case NdefRecord.TNF_MIME_MEDIA:
                return "MIME Media";
            case NdefRecord.TNF_ABSOLUTE_URI:
                return "Absolute URI";
            case NdefRecord.TNF_EXTERNAL_TYPE:
                return "External Type";
            case NdefRecord.TNF_UNKNOWN:
                return "Unknown";
            case NdefRecord.TNF_UNCHANGED:
                return "Unchanged";
            default:
                return "Reserved (" + tnf + ")";
        }
    }

    private String getTextFromNdefRecord(NdefRecord record) {
        try {
            byte[] payload = record.getPayload();

            // Controlla se è un record di testo
            if (record.getTnf() == NdefRecord.TNF_WELL_KNOWN &&
                Arrays.equals(record.getType(), NdefRecord.RTD_TEXT)) {

                // Il primo byte contiene informazioni sulla codifica e lunghezza del codice lingua
                String textEncoding = ((payload[0] & 128) == 0) ? "UTF-8" : "UTF-16";
                int languageCodeLength = payload[0] & 0063;

                // Estrae il testo saltando il codice lingua
                return new String(payload, languageCodeLength + 1,
                                payload.length - languageCodeLength - 1, textEncoding);
            } else {
                // Per altri tipi di record, prova a interpretare come testo UTF-8
                return new String(payload, "UTF-8");
            }
        } catch (UnsupportedEncodingException e) {
            return "Errore nella decodifica: " + e.getMessage();
        }
    }

    private void loadAndShowSavedToken() {
        SharedPreferences prefs = getSharedPreferences(PREFS_NAME, MODE_PRIVATE);
        String savedToken = prefs.getString(TOKEN_KEY, null);

        if (savedToken != null && !savedToken.isEmpty()) {
            Log.d(TAG, "Loading saved token: " + savedToken);
            showToken(savedToken);
            Toast.makeText(this, "Token caricato: " + savedToken, Toast.LENGTH_SHORT).show();
        } else {
            Log.d(TAG, "No saved token found");
        }
    }
}
