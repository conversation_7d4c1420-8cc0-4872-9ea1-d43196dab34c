package com.example.jettimbrature;

import android.Manifest;
import android.content.Intent;
import android.content.SharedPreferences;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.widget.Button;
import android.widget.FrameLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.activity.EdgeToEdge;
import androidx.appcompat.app.AlertDialog;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import androidx.core.graphics.Insets;
import androidx.core.view.ViewCompat;
import androidx.core.view.WindowInsetsCompat;

import com.google.zxing.ResultPoint;
import com.journeyapps.barcodescanner.BarcodeCallback;
import com.journeyapps.barcodescanner.BarcodeResult;
import com.journeyapps.barcodescanner.DecoratedBarcodeView;
import com.journeyapps.barcodescanner.DefaultDecoderFactory;

import java.util.Arrays;
import java.util.Collection;
import java.util.List;

public class ScanActivity extends AppCompatActivity {

    private static final String TAG = "JetTimbrature";
    private static final int CAMERA_PERMISSION_REQUEST = 100;

    // SharedPreferences per salvare il token
    private static final String PREFS_NAME = "JetTimbraturePrefs";
    private static final String TOKEN_KEY = "auth_token";

    private DecoratedBarcodeView barcodeView;
    private TextView statusTextView;
    private Button toggleFlashButton;
    private Button manualEntryButton;
    private FrameLayout cameraContainer;

    private boolean isFlashOn = false;
    private boolean isScanning = false;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        EdgeToEdge.enable(this);
        setContentView(R.layout.activity_scan);
        ViewCompat.setOnApplyWindowInsetsListener(findViewById(R.id.main), (v, insets) -> {
            Insets systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars());
            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom);
            return insets;
        });

        // Inizializza le view
        initializeViews();

        // Configura i pulsanti
        setupButtons();

        // Controlla se esiste già un token salvato
        if (checkExistingToken()) {
            return; // Se c'è un token, l'app passa alla ClockingActivity
        }

        // Verifica i permessi della fotocamera
        checkCameraPermission();
    }

    private void initializeViews() {
        statusTextView = findViewById(R.id.statusTextView);
        toggleFlashButton = findViewById(R.id.toggleFlashButton);
        manualEntryButton = findViewById(R.id.manualEntryButton);
        cameraContainer = findViewById(R.id.cameraContainer);
    }

    private void setupButtons() {
        toggleFlashButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                toggleFlash();
            }
        });

        manualEntryButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                showManualEntryDialog();
            }
        });
    }

    private void checkCameraPermission() {
        if (ContextCompat.checkSelfPermission(this, Manifest.permission.CAMERA)
                != PackageManager.PERMISSION_GRANTED) {

            ActivityCompat.requestPermissions(this,
                new String[]{Manifest.permission.CAMERA},
                CAMERA_PERMISSION_REQUEST);
        } else {
            initializeCamera();
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, String[] permissions, int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);

        if (requestCode == CAMERA_PERMISSION_REQUEST) {
            if (grantResults.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                initializeCamera();
            } else {
                statusTextView.setText("Permesso fotocamera negato");
                Toast.makeText(this, "Permesso fotocamera necessario per scansionare QR Code",
                    Toast.LENGTH_LONG).show();
            }
        }
    }

    private void initializeCamera() {
        // Crea la DecoratedBarcodeView
        barcodeView = new DecoratedBarcodeView(this);

        // Configura i formati supportati (solo QR Code)
        Collection<com.google.zxing.BarcodeFormat> formats = Arrays.asList(
            com.google.zxing.BarcodeFormat.QR_CODE
        );
        barcodeView.getBarcodeView().setDecoderFactory(new DefaultDecoderFactory(formats));

        // Configura il callback per i risultati
        barcodeView.decodeContinuous(new BarcodeCallback() {
            @Override
            public void barcodeResult(BarcodeResult result) {
                handleQRCodeResult(result.getText());
            }

            @Override
            public void possibleResultPoints(List<ResultPoint> resultPoints) {
                // Non utilizzato per ora
            }
        });

        // Aggiunge la view al container
        cameraContainer.addView(barcodeView);

        statusTextView.setText("Fotocamera inizializzata. Inquadra un QR Code...");
        Log.d(TAG, "Camera initialized successfully");
    }

    private void handleQRCodeResult(String qrCodeContent) {
        Log.d(TAG, "QR Code scanned: " + qrCodeContent);

        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                // Controlla se il QR Code è un URL valido
                if (isValidTimbratureUrl(qrCodeContent)) {
                    String token = extractTokenFromUrl(qrCodeContent);
                    if (token != null && isValidToken(token)) {
                        // Token valido - salva e passa alla ClockingActivity
                        saveToken(token);
                        statusTextView.setText("QR Code valido! Apertura app...");
                        Toast.makeText(ScanActivity.this, "Autenticazione riuscita", Toast.LENGTH_SHORT).show();

                        Log.d(TAG, "Valid token found: " + token);
                        openClockingActivity();
                        return;
                    }
                }

                // QR Code non valido - pausa la scansione
                pauseScanning();
                statusTextView.setText("QR Code non valido - Attendi 2 secondi...");
                Toast.makeText(ScanActivity.this, "QR Code non valido", Toast.LENGTH_LONG).show();

                // Countdown visivo
                showCountdown();

                // Riprende la scansione dopo 2 secondi
                statusTextView.postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        if (isScanning) {
                            resumeScanning();
                            statusTextView.setText("Pronto per scansionare...");
                        }
                    }
                }, 2000);
            }
        });
    }

    private void toggleFlash() {
        if (barcodeView != null) {
            if (isFlashOn) {
                barcodeView.setTorchOff();
                isFlashOn = false;
                toggleFlashButton.setText("Flash ON");
                Log.d(TAG, "Flash turned OFF");
            } else {
                barcodeView.setTorchOn();
                isFlashOn = true;
                toggleFlashButton.setText("Flash OFF");
                Log.d(TAG, "Flash turned ON");
            }
        }
    }

    private void showManualEntryDialog() {
        AlertDialog.Builder builder = new AlertDialog.Builder(this);
        builder.setTitle("Inserimento Manuale");
        builder.setMessage("Funzionalità di inserimento manuale non ancora implementata");
        builder.setPositiveButton("OK", null);
        builder.show();
    }

    @Override
    protected void onResume() {
        super.onResume();
        if (barcodeView != null) {
            barcodeView.resume();
            isScanning = true;
            statusTextView.setText("Scansione attiva...");
            Log.d(TAG, "Camera resumed");
        }
    }

    @Override
    protected void onPause() {
        super.onPause();
        if (barcodeView != null) {
            barcodeView.pause();
            isScanning = false;
            Log.d(TAG, "Camera paused");
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (barcodeView != null) {
            barcodeView.pause();
            Log.d(TAG, "Camera destroyed");
        }
    }

    // Metodi per gestione token e URL

    private boolean checkExistingToken() {
        SharedPreferences prefs = getSharedPreferences(PREFS_NAME, MODE_PRIVATE);
        String savedToken = prefs.getString(TOKEN_KEY, null);

        if (savedToken != null && !savedToken.isEmpty()) {
            Log.d(TAG, "Existing token found: " + savedToken);
            statusTextView.setText("Token esistente trovato. Apertura app...");
            openClockingActivity();
            return true;
        }

        Log.d(TAG, "No existing token found");
        return false;
    }

    private boolean isValidTimbratureUrl(String url) {
        try {
            Uri uri = Uri.parse(url);
            return "https".equals(uri.getScheme()) &&
                   "timbrature.jethr.com".equals(uri.getHost()) &&
                   uri.getPath() != null &&
                   uri.getPath().startsWith("/attiva/");
        } catch (Exception e) {
            Log.w(TAG, "Error parsing URL: " + e.getMessage());
            return false;
        }
    }

    private String extractTokenFromUrl(String url) {
        try {
            Uri uri = Uri.parse(url);
            String path = uri.getPath();
            if (path != null && path.startsWith("/attiva/")) {
                String token = path.substring("/attiva/".length());
                Log.d(TAG, "Extracted token: " + token);
                return token;
            }
        } catch (Exception e) {
            Log.w(TAG, "Error extracting token: " + e.getMessage());
        }
        return null;
    }

    private boolean isValidToken(String token) {
        // Verifica che il token sia alfanumerico e non vuoto
        boolean valid = token != null && token.matches("^[a-zA-Z0-9]+$") && token.length() > 0;
        Log.d(TAG, "Token validation: " + token + " -> " + valid);
        return valid;
    }

    private void saveToken(String token) {
        SharedPreferences prefs = getSharedPreferences(PREFS_NAME, MODE_PRIVATE);
        SharedPreferences.Editor editor = prefs.edit();
        editor.putString(TOKEN_KEY, token);
        editor.apply();

        Log.d(TAG, "Token saved: " + token);
    }

    private void openClockingActivity() {
        Intent intent = new Intent(this, ClockingActivity.class);
        startActivity(intent);
        finish(); // Chiude la ScanActivity
        Log.d(TAG, "Opening ClockingActivity");
    }

    private void pauseScanning() {
        if (barcodeView != null) {
            barcodeView.pauseAndWait();
            Log.d(TAG, "Scanning paused for error handling");
        }
    }

    private void resumeScanning() {
        if (barcodeView != null) {
            barcodeView.resume();
            Log.d(TAG, "Scanning resumed after error");
        }
    }

    private void showCountdown() {
        // Countdown da 2 a 1
        statusTextView.postDelayed(new Runnable() {
            @Override
            public void run() {
                if (isScanning) {
                    statusTextView.setText("QR Code non valido - Attendi 1 secondo...");
                }
            }
        }, 1000);
    }
}
