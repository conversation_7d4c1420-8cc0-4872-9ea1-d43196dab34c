package com.example.jettimbrature;

import android.Manifest;
import android.content.pm.PackageManager;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.widget.Button;
import android.widget.FrameLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.activity.EdgeToEdge;
import androidx.appcompat.app.AlertDialog;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import androidx.core.graphics.Insets;
import androidx.core.view.ViewCompat;
import androidx.core.view.WindowInsetsCompat;

import com.google.zxing.ResultPoint;
import com.journeyapps.barcodescanner.BarcodeCallback;
import com.journeyapps.barcodescanner.BarcodeResult;
import com.journeyapps.barcodescanner.DecoratedBarcodeView;
import com.journeyapps.barcodescanner.DefaultDecoderFactory;

import java.util.Arrays;
import java.util.Collection;
import java.util.List;

public class ScanActivity extends AppCompatActivity {

    private static final String TAG = "JetTimbrature";
    private static final int CAMERA_PERMISSION_REQUEST = 100;

    private DecoratedBarcodeView barcodeView;
    private TextView statusTextView;
    private Button toggleFlashButton;
    private Button manualEntryButton;
    private FrameLayout cameraContainer;

    private boolean isFlashOn = false;
    private boolean isScanning = false;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        EdgeToEdge.enable(this);
        setContentView(R.layout.activity_scan);
        ViewCompat.setOnApplyWindowInsetsListener(findViewById(R.id.main), (v, insets) -> {
            Insets systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars());
            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom);
            return insets;
        });

        // Inizializza le view
        initializeViews();

        // Configura i pulsanti
        setupButtons();

        // Verifica i permessi della fotocamera
        checkCameraPermission();
    }

    private void initializeViews() {
        statusTextView = findViewById(R.id.statusTextView);
        toggleFlashButton = findViewById(R.id.toggleFlashButton);
        manualEntryButton = findViewById(R.id.manualEntryButton);
        cameraContainer = findViewById(R.id.cameraContainer);
    }

    private void setupButtons() {
        toggleFlashButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                toggleFlash();
            }
        });

        manualEntryButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                showManualEntryDialog();
            }
        });
    }

    private void checkCameraPermission() {
        if (ContextCompat.checkSelfPermission(this, Manifest.permission.CAMERA)
                != PackageManager.PERMISSION_GRANTED) {

            ActivityCompat.requestPermissions(this,
                new String[]{Manifest.permission.CAMERA},
                CAMERA_PERMISSION_REQUEST);
        } else {
            initializeCamera();
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, String[] permissions, int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);

        if (requestCode == CAMERA_PERMISSION_REQUEST) {
            if (grantResults.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                initializeCamera();
            } else {
                statusTextView.setText("Permesso fotocamera negato");
                Toast.makeText(this, "Permesso fotocamera necessario per scansionare QR Code",
                    Toast.LENGTH_LONG).show();
            }
        }
    }

    private void initializeCamera() {
        // Crea la DecoratedBarcodeView
        barcodeView = new DecoratedBarcodeView(this);

        // Configura i formati supportati (solo QR Code)
        Collection<com.google.zxing.BarcodeFormat> formats = Arrays.asList(
            com.google.zxing.BarcodeFormat.QR_CODE
        );
        barcodeView.getBarcodeView().setDecoderFactory(new DefaultDecoderFactory(formats));

        // Configura il callback per i risultati
        barcodeView.decodeContinuous(new BarcodeCallback() {
            @Override
            public void barcodeResult(BarcodeResult result) {
                handleQRCodeResult(result.getText());
            }

            @Override
            public void possibleResultPoints(List<ResultPoint> resultPoints) {
                // Non utilizzato per ora
            }
        });

        // Aggiunge la view al container
        cameraContainer.addView(barcodeView);

        statusTextView.setText("Fotocamera inizializzata. Inquadra un QR Code...");
        Log.d(TAG, "Camera initialized successfully");
    }

    private void handleQRCodeResult(String qrCodeContent) {
        Log.d(TAG, "QR Code scanned: " + qrCodeContent);

        // Per ora tutti i QR Code restituiscono errore come richiesto
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                statusTextView.setText("QR Code non valido");
                Toast.makeText(ScanActivity.this, "QR Code non valido", Toast.LENGTH_LONG).show();

                // Riprende la scansione dopo 2 secondi
                statusTextView.postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        if (isScanning) {
                            statusTextView.setText("Pronto per scansionare...");
                        }
                    }
                }, 2000);
            }
        });
    }

    private void toggleFlash() {
        if (barcodeView != null) {
            if (isFlashOn) {
                barcodeView.setTorchOff();
                isFlashOn = false;
                toggleFlashButton.setText("Flash ON");
                Log.d(TAG, "Flash turned OFF");
            } else {
                barcodeView.setTorchOn();
                isFlashOn = true;
                toggleFlashButton.setText("Flash OFF");
                Log.d(TAG, "Flash turned ON");
            }
        }
    }

    private void showManualEntryDialog() {
        AlertDialog.Builder builder = new AlertDialog.Builder(this);
        builder.setTitle("Inserimento Manuale");
        builder.setMessage("Funzionalità di inserimento manuale non ancora implementata");
        builder.setPositiveButton("OK", null);
        builder.show();
    }

    @Override
    protected void onResume() {
        super.onResume();
        if (barcodeView != null) {
            barcodeView.resume();
            isScanning = true;
            statusTextView.setText("Scansione attiva...");
            Log.d(TAG, "Camera resumed");
        }
    }

    @Override
    protected void onPause() {
        super.onPause();
        if (barcodeView != null) {
            barcodeView.pause();
            isScanning = false;
            Log.d(TAG, "Camera paused");
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (barcodeView != null) {
            barcodeView.pause();
            Log.d(TAG, "Camera destroyed");
        }
    }
}
