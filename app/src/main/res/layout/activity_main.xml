<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools" android:id="@+id/main" android:layout_width="match_parent" android:layout_height="match_parent" android:padding="16dp" tools:context=".MainActivity">

    <TextView android:id="@+id/titleTextView" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="Lettore Badge NFC" android:textSize="24sp" android:textStyle="bold" android:layout_marginTop="32dp" app:layout_constraintTop_toTopOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintEnd_toEndOf="parent" />

    <TextView android:id="@+id/tokenTextView" android:layout_width="0dp" android:layout_height="wrap_content" android:text="" android:textSize="14sp" android:textColor="#2196F3" android:textAlignment="center" android:layout_marginTop="8dp" android:background="#E3F2FD" android:padding="8dp" android:visibility="gone" app:layout_constraintTop_toBottomOf="@id/titleTextView" app:layout_constraintStart_toStartOf="parent" app:layout_constraintEnd_toEndOf="parent" />

    <TextView android:id="@+id/instructionTextView" android:layout_width="0dp" android:layout_height="wrap_content" android:text="Avvicina un badge NFC al dispositivo per leggere i dati" android:textSize="16sp" android:textAlignment="center" android:layout_marginTop="24dp" app:layout_constraintTop_toBottomOf="@id/tokenTextView" app:layout_constraintStart_toStartOf="parent" app:layout_constraintEnd_toEndOf="parent" />

    <TextView android:id="@+id/statusTextView" android:layout_width="0dp" android:layout_height="wrap_content" android:text="In attesa di badge NFC..." android:textSize="14sp" android:textColor="#666666" android:textAlignment="center" android:layout_marginTop="16dp" app:layout_constraintTop_toBottomOf="@id/instructionTextView" app:layout_constraintStart_toStartOf="parent" app:layout_constraintEnd_toEndOf="parent" />

    <ScrollView android:id="@+id/scrollView" android:layout_width="0dp" android:layout_height="0dp" android:layout_marginTop="24dp" android:layout_marginBottom="16dp" app:layout_constraintTop_toBottomOf="@id/statusTextView" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintEnd_toEndOf="parent">

        <TextView android:id="@+id/dataTextView" android:layout_width="match_parent" android:layout_height="wrap_content" android:text="" android:textSize="12sp" android:fontFamily="monospace" android:background="#F5F5F5" android:padding="12dp" android:textIsSelectable="true" />

    </ScrollView>

</androidx.constraintlayout.widget.ConstraintLayout>